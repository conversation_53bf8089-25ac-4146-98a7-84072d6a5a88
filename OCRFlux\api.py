# -*- coding: utf-8 -*-
from flask import Flask, request, jsonify,send_file, send_from_directory
from flask_cors import CORS
import os
from werkzeug.utils import secure_filename
from core import process_pdf_to_markdown_via_service 
import time
from datetime import datetime
from 征信拆分 import split_and_merge_pdf

app = Flask(__name__)
CORS(app)  # 启用CORS支持

# 获取当前文件所在目录的绝对路径
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
# 创建绝对路径的data目录
DATA_FOLDER = os.path.join(BASE_DIR, 'data')
if not os.path.exists(DATA_FOLDER):
    os.makedirs(DATA_FOLDER)

def get_timestamp_filename(filename):
    """生成带时间戳的文件名"""
    # 确保文件名安全
    safe_name = secure_filename(filename)

    # 分离文件名和扩展名
    name, ext = os.path.splitext(safe_name)

    # 如果文件名被清空，使用默认名
    if not name:
        name = "file"

    # 添加时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    return f"{name}_{timestamp}{ext}"


@app.route('/api/credit/word', methods=['POST'])
def download_word():
    data = request.get_json()
    if not data or 'content' not in data:
        return jsonify({'success': False, 'error': '缺少markdown内容'}), 400
    md_content = data['content']
    # 生成唯一文件名
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    md_filename = f"{timestamp}.md"
    md_filename2 = f"{timestamp}"
    docx_filename = f"{timestamp}.docx"
    md_path = os.path.join(DATA_FOLDER, md_filename)
    md_path2 = os.path.join(DATA_FOLDER, md_filename2)
    docx_path = os.path.join(DATA_FOLDER, docx_filename)
    # 保存markdown内容
    with open(md_path, 'w', encoding='utf-8') as f:
        f.write(md_content)
    # 调用Markdown2docx生成word
    from Markdown2docx import Markdown2docx
    project = Markdown2docx(md_path2)
    project.eat_soup()
    project.save()
    # 返回word文件
    return send_file(docx_path, as_attachment=True, download_name=docx_filename)

@app.route('/api/credit/upload', methods=['POST'])
def zx_upload():
    if request.method == 'POST':
        if 'file' not in request.files:
            return jsonify({"success": False, "error": "没有文件"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "error": "没有选择文件"}), 400

        # 新增 page_range 参数获取，支持 form-data 或 query
        page_range = request.form.get('page_range')
        print('客户提取的内容:', page_range)

        if file:
            filename = secure_filename(file.filename)
            if not filename:
                filename = "uploaded_file"
            # 添加时间戳到文件名
            timestamp_filename = get_timestamp_filename(filename)
            file_path = os.path.join(DATA_FOLDER, timestamp_filename)
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            file.save(file_path)

            # 生成markdown文件名（使用带时间戳的文件名）
            base_name = os.path.splitext(timestamp_filename)[0]
            markdown_file = os.path.join(DATA_FOLDER, f"{base_name}_result.md")
            # 临时文件夹 保存pdf拆分后的图片
            temp_image_path = os.path.join(DATA_FOLDER, base_name)
            try:
                # 这里可将 extract_content 传递给后续处理函数（如需用到）
                result = process_pdf_to_markdown_via_service(file_path, temp_image_path, page_range)

                # 保存 result 到 markdown 文件
                with open(markdown_file, 'w', encoding='utf-8') as f:
                    f.write(str(result))

                return jsonify({"success": True, "result": result, "extract_content": page_range})
            except Exception as e:
                return jsonify({"success": False, "error": str(e)})

    return jsonify({"message": "请使用POST方法上传文件"})


@app.route('/api/credit/split', methods=['POST'])
def split_merge_pdf():
    if 'file' not in request.files:
        return jsonify({"success": False, "error": "没有文件"}), 400
    file = request.files['file']
    if file.filename == '':
        return jsonify({"success": False, "error": "没有选择文件"}), 400
    filename = secure_filename(file.filename)
    if not filename:
        filename = "uploaded_file.pdf"
    # 添加时间戳到文件名，避免冲突
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    input_pdf_filename = f"{os.path.splitext(filename)[0]}_{timestamp}.pdf"
    input_pdf_path = os.path.join(DATA_FOLDER, input_pdf_filename)
    file.save(input_pdf_path)
    # 生成输出PDF路径
    output_pdf_filename = f"{os.path.splitext(filename)[0]}_{timestamp}_split_merged.pdf"
    output_pdf_path = os.path.join(DATA_FOLDER, output_pdf_filename)
    try:
        split_and_merge_pdf(input_pdf_path, output_pdf_path)
        # 构造可访问的URL（假设前端通过 /data/ 访问 data 目录下的文件）
        file_url = request.host_url.rstrip('/') + '/data/' + output_pdf_filename
        return jsonify({"success": True, "url": file_url, "filename": output_pdf_filename})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/data/<path:filename>')
def serve_data_file(filename):
    return send_from_directory(DATA_FOLDER, filename)

## 征信报告ocr识别 5002端口 
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5002, debug=True)
