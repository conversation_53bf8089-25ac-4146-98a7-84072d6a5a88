import asyncio
import time
import os
import sys
from pathlib import Path
from argparse import Namespace
from ocrflux.client import request

def process_pdf_to_markdown_via_service(file_path, server_url="http://localhost", server_port=30024):
    """
    通过本地服务处理PDF文件并转换为Markdown格式，记录耗时

    Args:
        file_path: PDF文件路径
        server_url: 服务器URL
        server_port: 服务器端口
    """
    print(f"开始处理文件: {file_path}")

    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 - {file_path}")
        return

    # 生成输出文件路径（同目录下，同名但扩展名为.md）
    input_path = Path(file_path)
    output_path = input_path.with_suffix('.md')

    print(f"输出文件将保存为: {output_path}")
    print(f"连接服务器: {server_url}:{server_port}")

    # 记录开始时间
    start_time = time.time()

    try:
        # 配置请求参数
        args = Namespace(
            model="/root/autodl-tmp/OCRFlux/models/OCRFlux-3B",  # 这个路径在服务端配置
            skip_cross_page_merge=False,
            max_page_retries=1,
            url=server_url,
            port=server_port,
        )

        # 发送请求到服务
        print("正在发送请求到OCRFlux服务...")
        request_start = time.time()
        result = asyncio.run(request(args, file_path))
        request_time = time.time() - request_start
        print(f"服务请求完成，耗时: {request_time:.2f} 秒")

        if result != None:
            document_markdown = result['document_text']

            # 保存到MD文件
            print("正在保存Markdown文件...")
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(document_markdown)

            # 计算总耗时
            total_time = time.time() - start_time

            # 输出结果
            print("\n" + "="*50)
            print("处理完成!")
            print(f"输入文件: {file_path}")
            print(f"输出文件: {output_path}")
            print(f"服务器: {server_url}:{server_port}")
            print(f"服务请求耗时: {request_time:.2f} 秒")
            print(f"总耗时: {total_time:.2f} 秒")
            print("="*50)

            # 显示部分内容预览
            print("\n内容预览:")
            print("-" * 30)
            preview = document_markdown[:500] + "..." if len(document_markdown) > 500 else document_markdown
            print(preview)
            print("-" * 30)

        else:
            print("错误: OCR服务解析失败")
            total_time = time.time() - start_time
            print(f"失败前耗时: {total_time:.2f} 秒")

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        total_time = time.time() - start_time
        print(f"失败前耗时: {total_time:.2f} 秒")

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # 默认文件路径
        file_path = 'test.pdf'
        print(f"未指定文件路径，使用默认路径: {file_path}")
        print("使用方法: python client.py <PDF文件路径> [服务器URL] [端口]")

    # 可选的服务器配置参数
    server_url = sys.argv[2] if len(sys.argv) > 2 else "http://localhost"
    server_port = int(sys.argv[3]) if len(sys.argv) > 3 else 30024

    # 处理文件
    process_pdf_to_markdown_via_service(file_path, server_url, server_port)


'''
    用本方法运行，必须开启vllm 服务。(vllm serve $model_path --port $port --max-model-len 8192 --gpu_memory_utilization 0.8 )

    # 方法1: 只指定文件路径（使用默认服务器）
    python client.py /path/to/your/file.pdf

    # 方法2: 指定文件路径和服务器
    python client.py /path/to/your/file.pdf http://localhost 30024

    # 方法3: 使用默认设置
    python client.py

    # 示例：处理拆分后的页面
    python client.py split_pages/page_02_left.pdf

'''    