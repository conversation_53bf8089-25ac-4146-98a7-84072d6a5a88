import fitz  # PyMuPDF
import os

def split_pages_individually(input_pdf_path, output_dir="split_pages"):
    """
    将PDF每页拆分成左右两部分，并单独保存每个部分
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 打开原始PDF
    source_pdf = fitz.open(input_pdf_path)

    print(f"开始拆分PDF: {input_pdf_path}")
    print(f"总页数: {len(source_pdf)}")
    print(f"输出目录: {output_dir}")

    for page_num in range(len(source_pdf)):
        page = source_pdf[page_num]
        rect = page.rect

        # 检查页面旋转信息
        rotation = page.rotation
        print(f"第 {page_num + 1} 页信息:")
        print(f"  尺寸: {rect.width} x {rect.height}")
        print(f"  旋转角度: {rotation}°")

        # 计算分割位置（宽度的50%）
        mid_x = rect.width / 2

        # 左半部分和右半部分的矩形区域
        left_rect = fitz.Rect(0, 0, mid_x, rect.height)
        right_rect = fitz.Rect(mid_x, 0, rect.width, rect.height)

        # 创建左半部分PDF
        left_pdf = fitz.open()
        left_page = left_pdf.new_page(width=mid_x, height=rect.height)
        # 如果原页面有旋转，需要反向旋转来显示正确的内容
        if rotation != 0:
            # 反向旋转以显示正确的内容方向
            left_page.show_pdf_page(left_page.rect, source_pdf, page_num, clip=left_rect, rotate=-rotation)
        else:
            left_page.show_pdf_page(left_page.rect, source_pdf, page_num, clip=left_rect)
        left_filename = f"{output_dir}/page_{page_num+1:02d}_left.pdf"
        left_pdf.save(left_filename)
        left_pdf.close()

        # 创建右半部分PDF
        right_pdf = fitz.open()
        right_page = right_pdf.new_page(width=rect.width - mid_x, height=rect.height)
        # 如果原页面有旋转，需要反向旋转来显示正确的内容
        if rotation != 0:
            # 反向旋转以显示正确的内容方向
            right_page.show_pdf_page(right_page.rect, source_pdf, page_num, clip=right_rect, rotate=-rotation)
        else:
            right_page.show_pdf_page(right_page.rect, source_pdf, page_num, clip=right_rect)
        right_filename = f"{output_dir}/page_{page_num+1:02d}_right.pdf"
        right_pdf.save(right_filename)
        right_pdf.close()

        print(f"第 {page_num + 1} 页拆分完成:")
        print(f"  左半部分: {left_filename}")
        print(f"  右半部分: {right_filename}")

    source_pdf.close()
    print(f"✅ 所有页面拆分完成! 文件保存在: {output_dir}")

def split_and_merge_pdf(input_pdf_path, output_pdf_path):
    """
    将PDF按页拆分，每页按宽度50%二次切分，最后合并成新PDF
    """
    # 打开原始PDF
    source_pdf = fitz.open(input_pdf_path)
    # 创建新的PDF文档用于输出
    output_pdf = fitz.open()

    print(f"开始处理PDF: {input_pdf_path}")
    print(f"总页数: {len(source_pdf)}")

    for page_num in range(len(source_pdf)):
        page = source_pdf[page_num]
        rect = page.rect
        rotation = page.rotation

        # 计算分割位置（宽度的50%）
        mid_x = rect.width / 2

        # 左半部分和右半部分的矩形区域
        left_rect = fitz.Rect(0, 0, mid_x, rect.height)
        right_rect = fitz.Rect(mid_x, 0, rect.width, rect.height)

        # 判断是否为偶数页（第2,4,6...页）
        if (page_num + 1) % 2 == 0:  # 偶数页，先添加右半部分，再添加左半部分
            # 先添加右半部分
            right_page = output_pdf.new_page(width=rect.width - mid_x, height=rect.height)
            if rotation != 0:
                right_page.show_pdf_page(right_page.rect, source_pdf, page_num, clip=right_rect, rotate=-rotation)
            else:
                right_page.show_pdf_page(right_page.rect, source_pdf, page_num, clip=right_rect)

            # 再添加左半部分
            left_page = output_pdf.new_page(width=mid_x, height=rect.height)
            if rotation != 0:
                left_page.show_pdf_page(left_page.rect, source_pdf, page_num, clip=left_rect, rotate=-rotation)
            else:
                left_page.show_pdf_page(left_page.rect, source_pdf, page_num, clip=left_rect)

            print(f"处理第 {page_num + 1} 页完成 (偶数页，右→左顺序，旋转: {rotation}°)")
        else:  # 奇数页，先添加左半部分，再添加右半部分
            # 先添加左半部分
            left_page = output_pdf.new_page(width=mid_x, height=rect.height)
            if rotation != 0:
                left_page.show_pdf_page(left_page.rect, source_pdf, page_num, clip=left_rect, rotate=-rotation)
            else:
                left_page.show_pdf_page(left_page.rect, source_pdf, page_num, clip=left_rect)

            # 再添加右半部分
            right_page = output_pdf.new_page(width=rect.width - mid_x, height=rect.height)
            if rotation != 0:
                right_page.show_pdf_page(right_page.rect, source_pdf, page_num, clip=right_rect, rotate=-rotation)
            else:
                right_page.show_pdf_page(right_page.rect, source_pdf, page_num, clip=right_rect)

            print(f"处理第 {page_num + 1} 页完成 (奇数页，左→右顺序，旋转: {rotation}°)")

    # 保存处理后的PDF
    output_pdf.save(output_pdf_path)
    output_pdf.close()
    source_pdf.close()

    print(f"✅ 处理完成! 输出文件: {output_pdf_path}")

if __name__ == "__main__":
    input_file = "456.pdf"  # 替换为你的输入文件路径

    # 先单独拆分每页，方便查看效果
    print("=== 步骤1: 单独拆分每页 ===")
    split_pages_individually(input_file, "split_pages")

    print("\n=== 步骤2: 合并成最终PDF ===")
    output_file = "567.pdf"  # 替换为你的输出文件路径
    split_and_merge_pdf(input_file, output_file)